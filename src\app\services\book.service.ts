import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError, map } from 'rxjs';
import { delay, switchMap, catchError } from 'rxjs/operators';
import { Book, BookFilter } from '../models/book.model';
import { AuthService } from './auth.service';
import { SupabaseService } from './supabase.service';
import { DatabaseBook, DatabaseBookPage } from '../models/database.model';

@Injectable({
  providedIn: 'root'
})
export class BookService {
  private books: Book[] = [
    {
      id: '1',
      title: 'The Adventures of Luna',
      author: '<PERSON>',
      coverImage: 'https://images.pexels.com/photos/1831234/pexels-photo-1831234.jpeg?auto=compress&cs=tinysrgb&w=600',
      description: 'Join <PERSON> the cat on her magical adventure through the enchanted forest.',
      ageRange: '4-6',
      categories: ['Adventure', 'Fantasy', 'Animals'],
      pages: [
        { pageNumber: 1, content: 'Once upon a time, there was a curious little cat named <PERSON>.', image: 'https://images.pexels.com/photos/2061057/pexels-photo-2061057.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 2, content: 'Luna lived in a small cottage at the edge of an enchanted forest.', image: 'https://images.pexels.com/photos/4666754/pexels-photo-4666754.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 3, content: 'One sunny morning, Luna spotted a glowing butterfly outside her window.', image: 'https://images.pexels.com/photos/674318/pexels-photo-674318.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 4, content: 'She decided to follow it into the mysterious forest.', image: 'https://images.pexels.com/photos/15286/pexels-photo.jpg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 5, content: 'The butterfly led her to a magical clearing with talking animals.', image: 'https://images.pexels.com/photos/247431/pexels-photo-247431.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 6, content: 'Luna made many new friends and learned about the magic of friendship.', image: 'https://images.pexels.com/photos/1643457/pexels-photo-1643457.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 7, content: 'When the sun began to set, Luna said goodbye to her new friends.', image: 'https://images.pexels.com/photos/2835436/pexels-photo-2835436.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 8, content: 'She promised to return soon for more adventures.', image: 'https://images.pexels.com/photos/1314550/pexels-photo-1314550.jpeg?auto=compress&cs=tinysrgb&w=600' }
      ],
      createdAt: new Date('2023-01-15'),
      createdBy: '1'
    },
    {
      id: '2',
      title: 'Dinosaur Dreams',
      author: 'Michael Smith',
      coverImage: 'https://images.pexels.com/photos/3933881/pexels-photo-3933881.jpeg?auto=compress&cs=tinysrgb&w=600',
      description: 'Tommy goes on an adventure with friendly dinosaurs in this bedtime story.',
      ageRange: '5-8',
      categories: ['Dinosaurs', 'Bedtime', 'Adventure'],
      pages: [
        { pageNumber: 1, content: 'Tommy loved dinosaurs more than anything in the world.', image: 'https://images.pexels.com/photos/3933881/pexels-photo-3933881.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 2, content: 'Every night, he would fall asleep hugging his favorite dinosaur toy.', image: 'https://images.pexels.com/photos/3662845/pexels-photo-3662845.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 3, content: 'One night, Tommy\'s toy began to glow with a magical light.', image: 'https://images.pexels.com/photos/2346001/pexels-photo-2346001.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 4, content: 'Suddenly, he found himself in a prehistoric world with real dinosaurs!', image: 'https://images.pexels.com/photos/67112/pexels-photo-67112.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 5, content: 'The dinosaurs were friendly and invited Tommy to play with them.', image: 'https://images.pexels.com/photos/3651597/pexels-photo-3651597.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 6, content: 'They went on exciting adventures across mountains and valleys.', image: 'https://images.pexels.com/photos/9007174/pexels-photo-9007174.jpeg?auto=compress&cs=tinysrgb&w=600' }
      ],
      createdAt: new Date('2023-02-10'),
      createdBy: '1'
    },
    {
      id: '3',
      title: 'The Rainbow Fish',
      author: 'Emma Wilson',
      coverImage: 'https://images.pexels.com/photos/1692840/pexels-photo-1692840.jpeg?auto=compress&cs=tinysrgb&w=600',
      description: 'A beautiful fish learns the importance of sharing and friendship.',
      ageRange: '3-5',
      categories: ['Ocean', 'Friendship', 'Sharing'],
      pages: [
        { pageNumber: 1, content: 'In the deep blue sea lived the most beautiful fish in the ocean.', image: 'https://images.pexels.com/photos/1692840/pexels-photo-1692840.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 2, content: 'His scales were every shade of blue, green, and purple with sparkling silver scales.', image: 'https://images.pexels.com/photos/1321444/pexels-photo-1321444.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 3, content: 'The other fish admired his beautiful scales and asked if he would share.', image: 'https://images.pexels.com/photos/1174017/pexels-photo-1174017.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 4, content: 'But the Rainbow Fish was too proud and refused to share his scales.', image: 'https://images.pexels.com/photos/2155636/pexels-photo-2155636.jpeg?auto=compress&cs=tinysrgb&w=600' },
        { pageNumber: 5, content: 'Soon, the other fish stopped talking to him, and he felt very lonely.', image: 'https://images.pexels.com/photos/2157508/pexels-photo-2157508.jpeg?auto=compress&cs=tinysrgb&w=600' }
      ],
      createdAt: new Date('2023-03-05'),
      createdBy: '1'
    }
  ];

  private booksSubject = new BehaviorSubject<Book[]>(this.books);
  public books$ = this.booksSubject.asObservable();

  constructor(
    private authService: AuthService,
    private supabaseService: SupabaseService
  ) { }

  getBooks(filter?: BookFilter): Observable<Book[]> {
    let filteredBooks = [...this.books];

    if (filter) {
      if (filter.category) {
        filteredBooks = filteredBooks.filter(book =>
          book.categories.some(category =>
            category.toLowerCase().includes(filter.category!.toLowerCase())
          )
        );
      }

      if (filter.ageRange) {
        filteredBooks = filteredBooks.filter(book =>
          book.ageRange === filter.ageRange
        );
      }

      if (filter.searchTerm) {
        const term = filter.searchTerm.toLowerCase();
        filteredBooks = filteredBooks.filter(book =>
          book.title.toLowerCase().includes(term) ||
          book.author.toLowerCase().includes(term) ||
          book.description.toLowerCase().includes(term)
        );
      }
    }

    return of(filteredBooks).pipe(delay(500));
  }

  getBookById(id: string): Observable<Book> {
    const book = this.books.find(book => book.id === id);

    if (!book) {
      return throwError(() => new Error('Book not found'));
    }

    return of(book).pipe(delay(300));
  }

  addBook(bookData: Omit<Book, 'id' | 'createdAt' | 'createdBy'>): Observable<Book> {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.isAdmin) {
      return throwError(() => new Error('Unauthorized'));
    }

    const newBook: Book = {
      ...bookData,
      id: (this.books.length + 1).toString(),
      createdAt: new Date(),
      createdBy: currentUser.id
    };

    this.books.push(newBook);
    this.booksSubject.next([...this.books]);

    return of(newBook).pipe(delay(800));
  }

  updateBook(id: string, bookData: Partial<Book>): Observable<Book> {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.isAdmin) {
      return throwError(() => new Error('Unauthorized'));
    }

    const index = this.books.findIndex(book => book.id === id);

    if (index === -1) {
      return throwError(() => new Error('Book not found'));
    }

    const updatedBook = { ...this.books[index], ...bookData };
    this.books[index] = updatedBook;
    this.booksSubject.next([...this.books]);

    return of(updatedBook).pipe(delay(800));
  }

  deleteBook(id: string): Observable<boolean> {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.isAdmin) {
      return throwError(() => new Error('Unauthorized'));
    }

    const index = this.books.findIndex(book => book.id === id);

    if (index === -1) {
      return throwError(() => new Error('Book not found'));
    }

    this.books.splice(index, 1);
    this.booksSubject.next([...this.books]);

    return of(true).pipe(delay(800));
  }

  toggleFavorite(bookId: string): Observable<boolean> {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    const favorites = currentUser.favorites || [];
    let updatedFavorites: string[];

    if (favorites.includes(bookId)) {
      updatedFavorites = favorites.filter(id => id !== bookId);
    } else {
      updatedFavorites = [...favorites, bookId];
    }

    return this.authService.updateUserProfile({ favorites: updatedFavorites })
      .pipe(delay(300), delay(0), map(() => true));
  }

  updateReadingProgress(bookId: string, pageNumber: number): Observable<boolean> {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    const progress = currentUser.progress || {};
    const updatedProgress = { ...progress, [bookId]: pageNumber };

    return this.authService.updateUserProfile({ progress: updatedProgress })
      .pipe(delay(300), delay(0), map(() => true));
  }

  getFavoriteBooks(): Observable<Book[]> {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    const favorites = currentUser.favorites || [];
    const favoriteBooks = this.books.filter(book => favorites.includes(book.id));

    return of(favoriteBooks).pipe(delay(500));
  }
}