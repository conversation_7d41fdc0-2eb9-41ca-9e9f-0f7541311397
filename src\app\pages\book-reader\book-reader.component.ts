import { Component, OnInit, HostListener } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Book } from '../../models/book.model';
import { BookService } from '../../services/book.service';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-book-reader',
  imports: [CommonModule, RouterLink],
  template: `
    <div class="reader-container" [ngClass]="{'night-mode': isNightMode}">
      <div *ngIf="isLoading" class="loading">
        <p>Loading storybook...</p>
      </div>

      <div *ngIf="!isLoading && book" class="reader">
        <div class="reader-header">
          <button class="back-button" (click)="navigateBack()">
            <span>← Back</span>
          </button>

          <div class="reader-title">
            <h1>{{ book.title }}</h1>
            <p>by {{ book.author }}</p>
          </div>

          <div class="reader-controls">
            <button class="icon-button" (click)="toggleFavorite()" [attr.aria-label]="isFavorite ? 'Remove from favorites' : 'Add to favorites'">
              <span *ngIf="isFavorite">❤️</span>
              <span *ngIf="!isFavorite">🤍</span>
            </button>

            <button class="icon-button" (click)="toggleNightMode()" aria-label="Toggle night mode">
              <span *ngIf="isNightMode">☀️</span>
              <span *ngIf="!isNightMode">🌙</span>
            </button>
          </div>
        </div>

        <div class="book-progress">
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="progressPercentage"></div>
          </div>
          <div class="progress-text">
            Page {{ currentPage }} of {{ totalPages }}
          </div>
        </div>

        <div class="book-pages" [ngStyle]="getBookStyle()">
          <div class="page-content" [@pageTransition]="pageTransitionState">
            <div class="page-image" *ngIf="currentPageContent?.image">
              <img [src]="currentPageContent?.image" [alt]="'Illustration for page ' + currentPage">
            </div>
            <div class="page-text">
              {{ currentPageContent?.content }}
            </div>
          </div>

          <div class="page-navigation">
            <button
              [disabled]="currentPage === 1"
              (click)="previousPage()"
              class="nav-button prev"
            >
              ←
            </button>
            <button
              [disabled]="currentPage === totalPages"
              (click)="nextPage()"
              class="nav-button next"
            >
              →
            </button>
          </div>
        </div>
      </div>

      <div *ngIf="!isLoading && !book" class="error">
        <p>Sorry, we couldn't find that storybook.</p>
        <button routerLink="/books">Back to Catalog</button>
      </div>
    </div>
  `,
  styles: [`
    .reader-container {
      min-height: calc(100vh - 70px - 200px);
      padding: var(--spacing-lg) 0;
      background-color: var(--background);
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    .reader-container.night-mode {
      background-color: #1a1a2e;
      color: #e6e6e6;
    }

    .reader {
      max-width: 900px;
      margin: 0 auto;
      padding: 0 var(--spacing-md);
    }

    .reader-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .night-mode .reader-header {
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .back-button {
      background: none;
      border: none;
      color: var(--primary);
      font-weight: 600;
      cursor: pointer;
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-md);
      transition: background-color 0.3s ease;
    }

    .night-mode .back-button {
      color: #a499e0;
    }

    .back-button:hover {
      background-color: rgba(106, 90, 205, 0.1);
    }

    .night-mode .back-button:hover {
      background-color: rgba(106, 90, 205, 0.2);
    }

    .reader-title {
      text-align: center;
    }

    .reader-title h1 {
      margin-bottom: var(--spacing-xs);
      color: var(--primary-dark);
    }

    .night-mode .reader-title h1 {
      color: #a499e0;
    }

    .reader-title p {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    .night-mode .reader-title p {
      color: #aaa;
    }

    .reader-controls {
      display: flex;
      gap: var(--spacing-sm);
    }

    .icon-button {
      background: none;
      border: none;
      font-size: 1.2rem;
      cursor: pointer;
      padding: var(--spacing-xs);
      border-radius: var(--radius-full);
      transition: background-color 0.3s ease, transform 0.3s ease;
    }

    .icon-button:hover {
      background-color: rgba(106, 90, 205, 0.1);
      transform: scale(1.1);
    }

    .night-mode .icon-button:hover {
      background-color: rgba(106, 90, 205, 0.2);
    }

    .book-progress {
      margin-bottom: var(--spacing-lg);
    }

    .progress-bar {
      height: 6px;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: var(--radius-full);
      overflow: hidden;
      margin-bottom: var(--spacing-xs);
    }

    .night-mode .progress-bar {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .progress-fill {
      height: 100%;
      background-color: var(--accent);
      transition: width 0.3s ease;
    }

    .progress-text {
      text-align: center;
      font-size: 0.9rem;
      color: var(--text-secondary);
    }

    .night-mode .progress-text {
      color: #aaa;
    }

    .book-pages {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      padding: var(--spacing-xl);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      min-height: 500px;
      display: flex;
      flex-direction: column;
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
    }

    .night-mode .book-pages {
      background-color: #16213e;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    }

    .page-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .page-image {
      text-align: center;
    }

    .page-image img {
      max-width: 100%;
      max-height: 300px;
      border-radius: var(--radius-md);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .night-mode .page-image img {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      opacity: 0.9;
    }

    .page-text {
      font-size: 1.2rem;
      line-height: 1.8;
      color: var(--text-primary);
      text-align: center;
    }

    .night-mode .page-text {
      color: #e6e6e6;
    }

    .page-navigation {
      display: flex;
      justify-content: space-between;
      margin-top: auto;
    }

    .nav-button {
      background-color: var(--primary);
      color: white;
      border: none;
      width: 50px;
      height: 50px;
      border-radius: var(--radius-full);
      font-size: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: transform 0.3s ease, background-color 0.3s ease;
    }

    .nav-button:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-3px);
    }

    .nav-button:disabled {
      background-color: rgba(106, 90, 205, 0.3);
      cursor: not-allowed;
    }

    .night-mode .nav-button:disabled {
      background-color: rgba(106, 90, 205, 0.2);
    }

    .loading, .error {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 300px;
      text-align: center;
    }

    .error button {
      margin-top: var(--spacing-md);
      background-color: var(--primary);
      color: white;
      border: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.3s ease;
    }

    .error button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }

    @media (max-width: 768px) {
      .reader-header {
        flex-direction: column;
        gap: var(--spacing-md);
      }

      .back-button {
        align-self: flex-start;
      }

      .reader-controls {
        align-self: flex-end;
      }

      .book-pages {
        padding: var(--spacing-md);
      }

      .page-text {
        font-size: 1.1rem;
      }
    }
  `]
})
export class BookReaderComponent implements OnInit {
  book: Book | null = null;
  isLoading = true;
  currentPage = 1;
  totalPages = 0;
  isNightMode = false;
  isFavorite = false;
  pageTransitionState = 'current';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private bookService: BookService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const bookId = params.get('id');

      if (bookId) {
        this.loadBook(bookId);
      } else {
        this.isLoading = false;
      }
    });

    // Load night mode preference
    const nightModePreference = localStorage.getItem('night_mode');
    this.isNightMode = nightModePreference === 'true';
  }

  loadBook(bookId: string): void {
    this.isLoading = true;

    this.bookService.getBookById(bookId).subscribe({
      next: (book) => {
        this.book = book;
        this.totalPages = book.pages.length;

        // Check if user has this book as favorite
        const currentUser = this.authService.getCurrentUser();
        this.isFavorite = currentUser?.favorites?.includes(bookId) || false;

        // Load reading progress
        if (currentUser?.progress && currentUser.progress[bookId]) {
          this.currentPage = currentUser.progress[bookId];
        }

        this.isLoading = false;
      },
      error: () => {
        this.book = null;
        this.isLoading = false;
      }
    });
  }

  get currentPageContent() {
    if (!this.book || this.currentPage < 1 || this.currentPage > this.totalPages) {
      return null;
    }

    return this.book.pages.find(page => page.pageNumber === this.currentPage);
  }

  get progressPercentage(): number {
    if (!this.totalPages) return 0;
    return (this.currentPage / this.totalPages) * 100;
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.pageTransitionState = 'previous';
      setTimeout(() => {
        this.currentPage--;
        this.updateReadingProgress();
        this.pageTransitionState = 'current';
      }, 300);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.pageTransitionState = 'next';
      setTimeout(() => {
        this.currentPage++;
        this.updateReadingProgress();
        this.pageTransitionState = 'current';
      }, 300);
    }
  }

  updateReadingProgress(): void {
    if (this.book) {
      this.bookService.updateReadingProgress(this.book.id, this.currentPage).subscribe();
    }
  }

  toggleFavorite(): void {
    if (this.book) {
      this.bookService.toggleFavorite(this.book.id).subscribe({
        next: () => {
          this.isFavorite = !this.isFavorite;
        }
      });
    }
  }

  toggleNightMode(): void {
    this.isNightMode = !this.isNightMode;
    localStorage.setItem('night_mode', this.isNightMode.toString());
  }

  getBookStyle() {
    return {
      'background-color': this.isNightMode ? '#16213e' : '#ffffff',
      'color': this.isNightMode ? '#e6e6e6' : '#333333'
    };
  }

  navigateBack(): void {
    this.router.navigate(['/books']);
  }

  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.key === 'ArrowLeft') {
      this.previousPage();
    } else if (event.key === 'ArrowRight') {
      this.nextPage();
    }
  }
}