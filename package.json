{"name": "kids-storybook-catalog", "private": true, "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build"}, "dependencies": {"@angular/animations": "^19.2.13", "@angular/common": "^19.2.13", "@angular/compiler": "^19.2.13", "@angular/core": "^19.2.13", "@angular/forms": "^19.2.13", "@angular/platform-browser": "^19.2.13", "@angular/platform-browser-dynamic": "^19.2.13", "@angular/router": "^19.2.13", "rxjs": "^7.8.1", "tslib": "^2.5.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^19.2.13", "@angular/cli": "^19.2.13", "@angular/compiler-cli": "^19.2.13", "typescript": "^5.8.2"}}