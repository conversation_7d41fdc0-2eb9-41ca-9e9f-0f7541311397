import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Book } from '../../models/book.model';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-book-card',
  imports: [CommonModule],
  template: `
    <div class="book-card" [class.favorite]="isFavorite">
      <div class="book-cover">
        <img [src]="book.coverImage" [alt]="book.title">
        <div class="book-age-range">{{ book.ageRange }}</div>
        <button
          *ngIf="isAuthenticated"
          class="favorite-button"
          (click)="onFavoriteToggle($event)"
          [attr.aria-label]="isFavorite ? 'Remove from favorites' : 'Add to favorites'"
        >
          <span *ngIf="isFavorite">❤️</span>
          <span *ngIf="!isFavorite">🤍</span>
        </button>
      </div>

      <div class="book-info">
        <h3 class="book-title">{{ book.title }}</h3>
        <p class="book-author">by {{ book.author }}</p>

        <div class="book-categories">
          <span *ngFor="let category of book.categories" class="book-category">
            {{ category }}
          </span>
        </div>

        <p class="book-description">{{ book.description }}</p>

        <div class="book-actions">
          <button class="read-button" (click)="onReadClick()">Read Now</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .book-card {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .book-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
    }

    .book-card.favorite {
      box-shadow: 0 4px 12px rgba(255, 150, 66, 0.3);
    }

    .book-cover {
      position: relative;
      overflow: hidden;
      height: 200px;
    }

    .book-cover img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;
    }

    .book-card:hover .book-cover img {
      transform: scale(1.05);
    }

    .book-age-range {
      position: absolute;
      top: var(--spacing-sm);
      left: var(--spacing-sm);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-md);
      font-size: 0.8rem;
      font-weight: 600;
    }

    .favorite-button {
      position: absolute;
      top: var(--spacing-sm);
      right: var(--spacing-sm);
      background: rgba(255, 255, 255, 0.8);
      border: none;
      border-radius: var(--radius-full);
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
      cursor: pointer;
      transition: transform 0.3s ease, background-color 0.3s ease;
    }

    .favorite-button:hover {
      transform: scale(1.1);
      background: white;
    }

    .book-info {
      padding: var(--spacing-md);
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    .book-title {
      margin-bottom: var(--spacing-xs);
      font-size: 1.25rem;
      color: var(--text-primary);
    }

    .book-author {
      color: var(--text-secondary);
      margin-bottom: var(--spacing-sm);
      font-size: 0.9rem;
    }

    .book-categories {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-sm);
    }

    .book-category {
      background-color: rgba(106, 90, 205, 0.1);
      color: var(--primary);
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-full);
      font-size: 0.8rem;
      font-weight: 600;
    }

    .book-description {
      color: var(--text-secondary);
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: var(--spacing-md);
      flex: 1;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .book-actions {
      margin-top: auto;
    }

    .read-button {
      width: 100%;
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      padding: var(--spacing-sm) var(--spacing-md);
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.3s ease;
    }

    .read-button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }
  `]
})
export class BookCardComponent {
  @Input() book!: Book;
  @Input() isFavorite = false;

  @Output() readClicked = new EventEmitter<string>();
  @Output() favoriteToggled = new EventEmitter<string>();

  constructor(private authService: AuthService) {}

  get isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  onReadClick(): void {
    this.readClicked.emit(this.book.id);
  }

  onFavoriteToggle(event: Event): void {
    event.stopPropagation();
    this.favoriteToggled.emit(this.book.id);
  }
}