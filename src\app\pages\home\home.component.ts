import { Component, OnInit } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Observable } from 'rxjs';
import { Book } from '../../models/book.model';
import { User } from '../../models/user.model';
import { BookService } from '../../services/book.service';
import { AuthService } from '../../services/auth.service';
import { BookCardComponent } from '../../components/book-card/book-card.component';

@Component({
  selector: 'app-home',
  imports: [CommonModule, RouterLink, BookCardComponent],
  template: `
    <div class="hero">
      <div class="container hero-container">
        <div class="hero-content">
          <h1 class="hero-title">Discover <span class="highlight">Magical</span> Stories for Kids</h1>
          <p class="hero-description">
            Explore our collection of delightful storybooks that inspire imagination,
            creativity, and a love for reading in children of all ages.
          </p>
          <div class="hero-actions">
            <button class="primary" routerLink="/books">Browse Stories</button>
            <button class="secondary" *ngIf="!(currentUser$ | async)" routerLink="/register">Join StoryWonder</button>
          </div>
        </div>
        <div class="hero-image">
          <img src="https://images.pexels.com/photos/4861347/pexels-photo-4861347.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" alt="Children reading">
        </div>
      </div>
    </div>

    <section class="featured-books">
      <div class="container">
        <h2 class="section-title">Featured Storybooks</h2>
        <div class="books-grid">
          <ng-container *ngFor="let book of featuredBooks$ | async">
            <app-book-card
              [book]="book"
              [isFavorite]="isBookFavorited(book.id)"
              (readClicked)="onReadBook($event)"
              (favoriteToggled)="onToggleFavorite($event)"
            ></app-book-card>
          </ng-container>
        </div>
        <div class="view-all">
          <button routerLink="/books">View All Storybooks</button>
        </div>
      </div>
    </section>

    <section class="categories">
      <div class="container">
        <h2 class="section-title">Explore Categories</h2>
        <div class="categories-grid">
          <div class="category-card" routerLink="/books" [queryParams]="{category: 'Adventure'}">
            <div class="category-icon">🚀</div>
            <h3>Adventure</h3>
            <p>Exciting journeys and thrilling quests</p>
          </div>
          <div class="category-card" routerLink="/books" [queryParams]="{category: 'Fantasy'}">
            <div class="category-icon">✨</div>
            <h3>Fantasy</h3>
            <p>Magical worlds and enchanted tales</p>
          </div>
          <div class="category-card" routerLink="/books" [queryParams]="{category: 'Animals'}">
            <div class="category-icon">🦊</div>
            <h3>Animals</h3>
            <p>Adorable creatures and their stories</p>
          </div>
          <div class="category-card" routerLink="/books" [queryParams]="{category: 'Bedtime'}">
            <div class="category-icon">🌙</div>
            <h3>Bedtime</h3>
            <p>Perfect stories for sleepy time</p>
          </div>
        </div>
      </div>
    </section>

    <section class="age-groups">
      <div class="container">
        <h2 class="section-title">Stories by Age</h2>
        <div class="age-groups-grid">
          <div class="age-group" routerLink="/books" [queryParams]="{ageRange: '3-5'}">
            <h3>Ages 3-5</h3>
            <p>Simple and colorful stories for young readers</p>
          </div>
          <div class="age-group" routerLink="/books" [queryParams]="{ageRange: '4-6'}">
            <h3>Ages 4-6</h3>
            <p>Engaging tales with easy words and concepts</p>
          </div>
          <div class="age-group" routerLink="/books" [queryParams]="{ageRange: '5-8'}">
            <h3>Ages 5-8</h3>
            <p>Slightly longer stories with deeper themes</p>
          </div>
          <div class="age-group" routerLink="/books" [queryParams]="{ageRange: '8-12'}">
            <h3>Ages 8-12</h3>
            <p>Chapter books for more experienced readers</p>
          </div>
        </div>
      </div>
    </section>

    <section class="cta">
      <div class="container">
        <div class="cta-content">
          <h2>Begin Your Reading Adventure Today!</h2>
          <p>Create an account to access all our storybooks and save your favorites.</p>
          <button *ngIf="!(currentUser$ | async)" routerLink="/register">Sign Up - It's Free!</button>
          <button *ngIf="(currentUser$ | async)" routerLink="/books">Explore Stories</button>
        </div>
      </div>
    </section>
  `,
  styles: [`
    .hero {
      background-color: var(--primary-light);
      background-image: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
      color: white;
      padding: var(--spacing-xl) 0;
      margin-bottom: var(--spacing-xxl);
    }

    .hero-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-xl);
      align-items: center;
    }

    .hero-content {
      animation: slideUp 0.8s ease forwards;
    }

    .hero-title {
      font-size: 2.8rem;
      margin-bottom: var(--spacing-md);
      line-height: 1.2;
      color: white;
    }

    .hero-title .highlight {
      color: var(--accent);
      position: relative;
      display: inline-block;
    }

    .hero-title .highlight::after {
      content: '';
      position: absolute;
      bottom: 5px;
      left: 0;
      width: 100%;
      height: 8px;
      background-color: rgba(255, 150, 66, 0.3);
      z-index: -1;
      border-radius: var(--radius-sm);
    }

    .hero-description {
      font-size: 1.2rem;
      margin-bottom: var(--spacing-lg);
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.9);
    }

    .hero-actions {
      display: flex;
      gap: var(--spacing-md);
    }

    .hero-actions button {
      padding: var(--spacing-md) var(--spacing-xl);
      font-size: 1.1rem;
      font-weight: 600;
      border-radius: var(--radius-md);
      transition: all 0.3s ease;
    }

    .hero-actions button.primary {
      background-color: white;
      color: var(--primary);
    }

    .hero-actions button.primary:hover {
      background-color: var(--accent);
      color: white;
      transform: translateY(-3px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .hero-actions button.secondary {
      background-color: transparent;
      border: 2px solid white;
      color: white;
    }

    .hero-actions button.secondary:hover {
      background-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-3px);
    }

    .hero-image {
      animation: fadeIn 1s ease forwards;
      animation-delay: 0.3s;
      opacity: 0;
    }

    .hero-image img {
      width: 100%;
      border-radius: var(--radius-lg);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
      transform: rotate(2deg);
    }

    /* Featured Books Section */
    .featured-books {
      margin-bottom: var(--spacing-xxl);
    }

    .section-title {
      text-align: center;
      margin-bottom: var(--spacing-xl);
      color: var(--primary-dark);
      position: relative;
      display: inline-block;
      left: 50%;
      transform: translateX(-50%);
      padding-bottom: var(--spacing-sm);
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 30%;
      width: 40%;
      height: 3px;
      background-color: var(--accent);
      border-radius: var(--radius-full);
    }

    .books-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .view-all {
      text-align: center;
    }

    /* Categories Section */
    .categories {
      margin-bottom: var(--spacing-xxl);
      background-color: rgba(106, 90, 205, 0.05);
      padding: var(--spacing-xxl) 0;
    }

    .categories-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--spacing-lg);
    }

    .category-card {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      text-align: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      cursor: pointer;
    }

    .category-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }

    .category-icon {
      font-size: 2.5rem;
      margin-bottom: var(--spacing-md);
    }

    .category-card h3 {
      color: var(--primary);
      margin-bottom: var(--spacing-sm);
    }

    .category-card p {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    /* Age Groups Section */
    .age-groups {
      margin-bottom: var(--spacing-xxl);
    }

    .age-groups-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--spacing-lg);
    }

    .age-group {
      border: 2px solid var(--primary-light);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .age-group:hover {
      background-color: var(--primary);
      color: white;
      transform: scale(1.03);
    }

    .age-group:hover h3,
    .age-group:hover p {
      color: white;
    }

    .age-group h3 {
      color: var(--primary);
      margin-bottom: var(--spacing-sm);
    }

    .age-group p {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    /* CTA Section */
    .cta {
      background-color: var(--accent);
      color: white;
      padding: var(--spacing-xxl) 0;
      text-align: center;
    }

    .cta-content h2 {
      color: white;
      margin-bottom: var(--spacing-md);
      font-size: 2rem;
    }

    .cta-content p {
      margin-bottom: var(--spacing-lg);
      font-size: 1.1rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .cta-content button {
      background-color: white;
      color: var(--accent-dark);
      font-size: 1.1rem;
      padding: var(--spacing-md) var(--spacing-xl);
      border-radius: var(--radius-md);
      transition: all 0.3s ease;
    }

    .cta-content button:hover {
      background-color: var(--primary);
      color: white;
      transform: translateY(-3px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    /* Responsive Design */
    @media (max-width: 992px) {
      .hero-title {
        font-size: 2.4rem;
      }

      .books-grid,
      .categories-grid,
      .age-groups-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .hero-container {
        grid-template-columns: 1fr;
      }

      .hero-image {
        grid-row: 1;
        margin-bottom: var(--spacing-lg);
      }

      .hero-content {
        text-align: center;
      }

      .hero-actions {
        justify-content: center;
      }

      .hero-title {
        font-size: 2rem;
      }

      .books-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 576px) {
      .categories-grid,
      .age-groups-grid {
        grid-template-columns: 1fr;
      }

      .hero-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
      }
    }
  `]
})
export class HomeComponent implements OnInit {
  featuredBooks$!: Observable<Book[]>;
  currentUser$: Observable<User | null>;

  constructor(
    private bookService: BookService,
    private authService: AuthService,
    private router: Router
  ) {
    this.currentUser$ = this.authService.currentUser$;
  }

  ngOnInit(): void {
    this.featuredBooks$ = this.bookService.getBooks();
  }

  onReadBook(bookId: string): void {
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/books', bookId]);
    } else {
      this.router.navigate(['/login'], {
        queryParams: { returnUrl: `/books/${bookId}` }
      });
    }
  }

  onToggleFavorite(bookId: string): void {
    if (this.authService.isAuthenticated()) {
      this.bookService.toggleFavorite(bookId).subscribe();
    } else {
      this.router.navigate(['/login']);
    }
  }

  isBookFavorited(bookId: string): boolean {
    const currentUser = this.authService.getCurrentUser();
    return currentUser?.favorites?.includes(bookId) || false;
  }
}