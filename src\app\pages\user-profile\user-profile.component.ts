import { Component, OnInit } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Observable } from 'rxjs';
import { Book } from '../../models/book.model';
import { User } from '../../models/user.model';
import { BookService } from '../../services/book.service';
import { AuthService } from '../../services/auth.service';
import { BookCardComponent } from '../../components/book-card/book-card.component';

@Component({
  selector: 'app-user-profile',
  imports: [CommonModule, RouterLink, BookCardComponent],
  template: `
    <div class="profile-container">
      <div class="container">
        <div class="profile-header">
          <h1 class="profile-title">My Library</h1>
          <p class="profile-subtitle">Manage your favorite storybooks and reading progress</p>
        </div>

        <div class="profile-sections">
          <div class="profile-section">
            <div class="section-header">
              <h2>Favorites</h2>
              <p>Stories you've marked as favorites</p>
            </div>

            <div *ngIf="isLoadingFavorites" class="loading">
              <p>Loading your favorites...</p>
            </div>

            <div *ngIf="!isLoadingFavorites && (favoriteBooks$ | async)?.length === 0" class="empty-state">
              <p>You haven't added any favorites yet.</p>
              <button routerLink="/books">Browse Storybooks</button>
            </div>

            <div *ngIf="!isLoadingFavorites" class="books-grid">
              <ng-container *ngFor="let book of (favoriteBooks$ | async)">
                <app-book-card
                  [book]="book"
                  [isFavorite]="true"
                  (readClicked)="onReadBook($event)"
                  (favoriteToggled)="onToggleFavorite($event)"
                ></app-book-card>
              </ng-container>
            </div>
          </div>

          <div class="profile-section">
            <div class="section-header">
              <h2>Reading Progress</h2>
              <p>Continue reading where you left off</p>
            </div>

            <div *ngIf="isLoadingProgress" class="loading">
              <p>Loading your reading progress...</p>
            </div>

            <div *ngIf="!isLoadingProgress && progressBooks.length === 0" class="empty-state">
              <p>You haven't started reading any books yet.</p>
              <button routerLink="/books">Start Reading</button>
            </div>

            <div *ngIf="!isLoadingProgress && progressBooks.length > 0" class="progress-list">
              <div *ngFor="let item of progressBooks" class="progress-item">
                <div class="progress-info">
                  <h3>{{ item.book.title }}</h3>
                  <p>by {{ item.book.author }}</p>
                  <div class="progress-bar">
                    <div class="progress-fill" [style.width.%]="getProgressPercentage(item)"></div>
                  </div>
                  <p class="progress-text">
                    Page {{ item.currentPage }} of {{ item.totalPages }}
                    ({{ getProgressPercentage(item) }}% complete)
                  </p>
                </div>
                <div class="progress-actions">
                  <button (click)="onReadBook(item.book.id)">Continue Reading</button>
                </div>
              </div>
            </div>
          </div>

          <div class="profile-section">
            <div class="section-header">
              <h2>Account Settings</h2>
              <p>Manage your account information</p>
            </div>

            <div class="account-details">
              <div class="detail-group">
                <label>Username</label>
                <p>{{ (currentUser$ | async)?.username }}</p>
              </div>

              <div class="detail-group">
                <label>Email</label>
                <p>{{ (currentUser$ | async)?.email }}</p>
              </div>

              <div class="detail-group">
                <label>Account Type</label>
                <p>{{ (currentUser$ | async)?.isAdmin ? 'Administrator' : 'Reader' }}</p>
              </div>

              <div class="detail-group">
                <label>Member Since</label>
                <p>{{ (currentUser$ | async)?.createdAt | date:'mediumDate' }}</p>
              </div>

              <button class="logout-button" (click)="logout()">Log Out</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .profile-container {
      padding: var(--spacing-lg) 0 var(--spacing-xxl);
    }

    .profile-header {
      text-align: center;
      margin-bottom: var(--spacing-xl);
    }

    .profile-title {
      color: var(--primary-dark);
      margin-bottom: var(--spacing-sm);
    }

    .profile-subtitle {
      color: var(--text-secondary);
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .profile-sections {
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-xl);
    }

    .profile-section {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .section-header {
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .section-header h2 {
      color: var(--primary-dark);
      margin-bottom: var(--spacing-xs);
    }

    .section-header p {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    .books-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: var(--spacing-lg);
    }

    .loading, .empty-state {
      text-align: center;
      padding: var(--spacing-xl) 0;
      color: var(--text-secondary);
    }

    .empty-state button {
      margin-top: var(--spacing-md);
      background-color: var(--primary);
      color: white;
      border: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .empty-state button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }

    .progress-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .progress-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      background-color: rgba(106, 90, 205, 0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .progress-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .progress-info {
      flex: 1;
    }

    .progress-info h3 {
      color: var(--primary-dark);
      margin-bottom: var(--spacing-xs);
      font-size: 1.1rem;
    }

    .progress-info p {
      color: var(--text-secondary);
      font-size: 0.9rem;
      margin-bottom: var(--spacing-sm);
    }

    .progress-bar {
      height: 6px;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: var(--radius-full);
      overflow: hidden;
      margin-bottom: var(--spacing-xs);
    }

    .progress-fill {
      height: 100%;
      background-color: var(--accent);
      transition: width 0.3s ease;
    }

    .progress-text {
      font-size: 0.8rem !important;
    }

    .progress-actions button {
      background-color: var(--primary);
      color: white;
      border: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .progress-actions button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }

    .account-details {
      display: grid;
      gap: var(--spacing-md);
    }

    .detail-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }

    .detail-group label {
      font-weight: 600;
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    .detail-group p {
      color: var(--text-primary);
      padding: var(--spacing-sm) 0;
    }

    .logout-button {
      margin-top: var(--spacing-md);
      background-color: rgba(244, 67, 54, 0.1);
      color: var(--error);
      border: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
    }

    .logout-button:hover {
      background-color: var(--error);
      color: white;
    }

    @media (max-width: 768px) {
      .profile-sections {
        grid-template-columns: 1fr;
      }

      .progress-item {
        flex-direction: column;
        align-items: flex-start;
      }

      .progress-actions {
        margin-top: var(--spacing-md);
        align-self: flex-end;
      }
    }
  `]
})
export class UserProfileComponent implements OnInit {
  currentUser$: Observable<User | null>;
  favoriteBooks$!: Observable<Book[]>;
  progressBooks: { book: Book, currentPage: number, totalPages: number }[] = [];
  isLoadingFavorites = true;
  isLoadingProgress = true;

  constructor(
    private authService: AuthService,
    private bookService: BookService,
    private router: Router
  ) {
    this.currentUser$ = this.authService.currentUser$;
  }

  ngOnInit(): void {
    this.loadFavoriteBooks();
    this.loadProgressBooks();
  }

  loadFavoriteBooks(): void {
    this.isLoadingFavorites = true;

    this.favoriteBooks$ = this.bookService.getFavoriteBooks();

    // Set loading to false after a short delay
    setTimeout(() => {
      this.isLoadingFavorites = false;
    }, 500);
  }

  loadProgressBooks(): void {
    this.isLoadingProgress = true;

    const currentUser = this.authService.getCurrentUser();
    if (currentUser && currentUser.progress) {
      const progressEntries = Object.entries(currentUser.progress);

      if (progressEntries.length === 0) {
        this.isLoadingProgress = false;
        return;
      }

      // For each book with progress, load the book details
      let loadedBooks = 0;

      progressEntries.forEach(([bookId, pageNumber]) => {
        this.bookService.getBookById(bookId).subscribe({
          next: (book) => {
            this.progressBooks.push({
              book,
              currentPage: pageNumber,
              totalPages: book.pages.length
            });

            loadedBooks++;

            // When all books are loaded, set loading to false
            if (loadedBooks === progressEntries.length) {
              // Sort by progress (least complete first)
              this.progressBooks.sort((a, b) => {
                const progressA = a.currentPage / a.totalPages;
                const progressB = b.currentPage / b.totalPages;
                return progressA - progressB;
              });

              this.isLoadingProgress = false;
            }
          },
          error: () => {
            loadedBooks++;

            if (loadedBooks === progressEntries.length) {
              this.isLoadingProgress = false;
            }
          }
        });
      });
    } else {
      this.isLoadingProgress = false;
    }
  }

  getProgressPercentage(item: { currentPage: number, totalPages: number }): number {
    return Math.round((item.currentPage / item.totalPages) * 100);
  }

  onReadBook(bookId: string): void {
    this.router.navigate(['/books', bookId]);
  }

  onToggleFavorite(bookId: string): void {
    this.bookService.toggleFavorite(bookId).subscribe({
      next: () => {
        // Reload favorite books
        this.loadFavoriteBooks();
      }
    });
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/']);
  }
}