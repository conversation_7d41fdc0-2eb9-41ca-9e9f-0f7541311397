import { Injectable } from '@angular/core';
import { Observable, from, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { supabase, TABLES } from '../config/supabase.config';
import { 
  DatabaseUser, 
  DatabaseBook, 
  DatabaseBookPage, 
  DatabaseUserFavorite, 
  DatabaseUserProgress,
  SupabaseResponse,
  SupabaseListResponse 
} from '../models/database.model';

@Injectable({
  providedIn: 'root'
})
export class SupabaseService {
  private connectionStatus = new BehaviorSubject<boolean>(false);
  public connectionStatus$ = this.connectionStatus.asObservable();

  constructor() {
    this.testConnection();
  }

  // Test database connection
  private async testConnection(): Promise<void> {
    try {
      const { data, error } = await supabase.from(TABLES.BOOKS).select('count').limit(1);
      if (error) {
        console.error('Supabase connection error:', error);
        this.connectionStatus.next(false);
      } else {
        console.log('✅ Supabase connected successfully');
        this.connectionStatus.next(true);
      }
    } catch (error) {
      console.error('Supabase connection failed:', error);
      this.connectionStatus.next(false);
    }
  }

  // User operations
  createUser(user: Omit<DatabaseUser, 'id' | 'created_at' | 'updated_at'>): Observable<SupabaseResponse<DatabaseUser>> {
    return from(supabase.from(TABLES.USERS).insert(user).select().single());
  }

  getUserByEmail(email: string): Observable<SupabaseResponse<DatabaseUser>> {
    return from(supabase.from(TABLES.USERS).select('*').eq('email', email).single());
  }

  getUserById(id: string): Observable<SupabaseResponse<DatabaseUser>> {
    return from(supabase.from(TABLES.USERS).select('*').eq('id', id).single());
  }

  updateUser(id: string, updates: Partial<DatabaseUser>): Observable<SupabaseResponse<DatabaseUser>> {
    return from(supabase.from(TABLES.USERS).update(updates).eq('id', id).select().single());
  }

  // Book operations
  getAllBooks(): Observable<SupabaseListResponse<DatabaseBook>> {
    return from(supabase.from(TABLES.BOOKS).select('*').order('created_at', { ascending: false }));
  }

  getBookById(id: string): Observable<SupabaseResponse<DatabaseBook>> {
    return from(supabase.from(TABLES.BOOKS).select('*').eq('id', id).single());
  }

  createBook(book: Omit<DatabaseBook, 'id' | 'created_at' | 'updated_at'>): Observable<SupabaseResponse<DatabaseBook>> {
    return from(supabase.from(TABLES.BOOKS).insert(book).select().single());
  }

  updateBook(id: string, updates: Partial<DatabaseBook>): Observable<SupabaseResponse<DatabaseBook>> {
    return from(supabase.from(TABLES.BOOKS).update(updates).eq('id', id).select().single());
  }

  deleteBook(id: string): Observable<SupabaseResponse<any>> {
    return from(supabase.from(TABLES.BOOKS).delete().eq('id', id));
  }

  // Book pages operations
  getBookPages(bookId: string): Observable<SupabaseListResponse<DatabaseBookPage>> {
    return from(supabase.from(TABLES.BOOK_PAGES).select('*').eq('book_id', bookId).order('page_number'));
  }

  createBookPage(page: Omit<DatabaseBookPage, 'id' | 'created_at'>): Observable<SupabaseResponse<DatabaseBookPage>> {
    return from(supabase.from(TABLES.BOOK_PAGES).insert(page).select().single());
  }

  updateBookPage(id: string, updates: Partial<DatabaseBookPage>): Observable<SupabaseResponse<DatabaseBookPage>> {
    return from(supabase.from(TABLES.BOOK_PAGES).update(updates).eq('id', id).select().single());
  }

  deleteBookPage(id: string): Observable<SupabaseResponse<any>> {
    return from(supabase.from(TABLES.BOOK_PAGES).delete().eq('id', id));
  }

  // User favorites operations
  getUserFavorites(userId: string): Observable<SupabaseListResponse<DatabaseUserFavorite>> {
    return from(supabase.from(TABLES.USER_FAVORITES).select('*').eq('user_id', userId));
  }

  addToFavorites(userId: string, bookId: string): Observable<SupabaseResponse<DatabaseUserFavorite>> {
    return from(supabase.from(TABLES.USER_FAVORITES).insert({ user_id: userId, book_id: bookId }).select().single());
  }

  removeFromFavorites(userId: string, bookId: string): Observable<SupabaseResponse<any>> {
    return from(supabase.from(TABLES.USER_FAVORITES).delete().eq('user_id', userId).eq('book_id', bookId));
  }

  // User progress operations
  getUserProgress(userId: string): Observable<SupabaseListResponse<DatabaseUserProgress>> {
    return from(supabase.from(TABLES.USER_PROGRESS).select('*').eq('user_id', userId));
  }

  updateUserProgress(userId: string, bookId: string, currentPage: number, totalPages: number): Observable<SupabaseResponse<DatabaseUserProgress>> {
    return from(supabase.from(TABLES.USER_PROGRESS)
      .upsert({ user_id: userId, book_id: bookId, current_page: currentPage, total_pages: totalPages })
      .select().single());
  }

  // File upload operations
  uploadFile(bucket: string, path: string, file: File): Observable<any> {
    return from(supabase.storage.from(bucket).upload(path, file));
  }

  getPublicUrl(bucket: string, path: string): string {
    const { data } = supabase.storage.from(bucket).getPublicUrl(path);
    return data.publicUrl;
  }
}
