import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { delay, tap, map, switchMap, catchError } from 'rxjs/operators';
import { User, AuthResponse, LoginCredentials, RegisterCredentials } from '../models/user.model';
import { SupabaseService } from './supabase.service';
import { DatabaseUser } from '../models/database.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$: Observable<User | null> = this.currentUserSubject.asObservable();

  private readonly STORAGE_KEY = 'story_wonder_user';
  private readonly MOCK_USERS = [
    {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      isAdmin: true,
      favorites: [],
      progress: {},
      createdAt: new Date()
    },
    {
      id: '2',
      username: 'user',
      email: '<EMAIL>',
      password: 'user123',
      isAdmin: false,
      favorites: ['1', '3'],
      progress: { '1': 2, '3': 5 },
      createdAt: new Date()
    }
  ];

  constructor(private supabaseService: SupabaseService) {
    // Check for existing user session on startup
    this.loadUserFromStorage();
  }

  private loadUserFromStorage(): void {
    const storedUser = localStorage.getItem(this.STORAGE_KEY);
    if (storedUser) {
      try {
        const { user, token } = JSON.parse(storedUser) as AuthResponse;
        this.currentUserSubject.next(user);
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem(this.STORAGE_KEY);
      }
    }
  }

  login(credentials: LoginCredentials): Observable<AuthResponse> {
    // For demo purposes, we're using mock data
    const user = this.MOCK_USERS.find(
      u => u.email === credentials.email && u.password === credentials.password
    );

    if (!user) {
      return throwError(() => new Error('Invalid email or password'));
    }

    // Create a sanitized user object (without password)
    const { password, ...userWithoutPassword } = user;
    const authResponse: AuthResponse = {
      user: userWithoutPassword as User,
      token: `mock-jwt-token-${Date.now()}`
    };

    // Save to local storage and update current user
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(authResponse));
    this.currentUserSubject.next(userWithoutPassword as User);

    // Simulate network delay
    return of(authResponse).pipe(delay(800));
  }

  register(credentials: RegisterCredentials): Observable<AuthResponse> {
    // Check if user already exists
    if (this.MOCK_USERS.some(u => u.email === credentials.email)) {
      return throwError(() => new Error('Email already in use'));
    }

    // Create new user
    const newUser = {
      id: `${this.MOCK_USERS.length + 1}`,
      username: credentials.username,
      email: credentials.email,
      password: credentials.password,
      isAdmin: false,
      favorites: [],
      progress: {},
      createdAt: new Date()
    };

    // In a real app, we would save this to a database
    this.MOCK_USERS.push(newUser);

    // Create auth response (without password)
    const { password, ...userWithoutPassword } = newUser;
    const authResponse: AuthResponse = {
      user: userWithoutPassword as User,
      token: `mock-jwt-token-${Date.now()}`
    };

    // Save to local storage and update current user
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(authResponse));
    this.currentUserSubject.next(userWithoutPassword as User);

    // Simulate network delay
    return of(authResponse).pipe(delay(800));
  }

  logout(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    this.currentUserSubject.next(null);
  }

  isAuthenticated(): boolean {
    return !!this.currentUserSubject.value;
  }

  isAdmin(): boolean {
    const currentUser = this.currentUserSubject.value;
    return !!currentUser && currentUser.isAdmin;
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  updateUserProfile(updatedUser: Partial<User>): Observable<User> {
    const currentUser = this.currentUserSubject.value;

    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    // Update user data
    const updatedUserData = { ...currentUser, ...updatedUser };

    // Update in storage
    const storedData = JSON.parse(localStorage.getItem(this.STORAGE_KEY) || '{}') as AuthResponse;
    const updatedStoredData = {
      ...storedData,
      user: updatedUserData
    };

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedStoredData));
    this.currentUserSubject.next(updatedUserData);

    // Simulate network delay
    return of(updatedUserData).pipe(delay(800));
  }
}