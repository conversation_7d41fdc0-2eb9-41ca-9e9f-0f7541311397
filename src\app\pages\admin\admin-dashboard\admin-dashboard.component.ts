import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { Book } from '../../../models/book.model';
import { BookService } from '../../../services/book.service';

@Component({
  selector: 'app-admin-dashboard',
  template: `
    <div class="admin-container">
      <div class="container">
        <div class="admin-header">
          <h1 class="admin-title">Admin Dashboard</h1>
          <p class="admin-subtitle">Manage storybooks and content</p>
        </div>
        
        <div class="admin-actions">
          <button class="primary" routerLink="/admin/upload">Upload New Storybook</button>
        </div>
        
        <div class="admin-section">
          <div class="section-header">
            <h2>Manage Storybooks</h2>
            <p>Edit or delete existing storybooks</p>
          </div>
          
          <div *ngIf="isLoading" class="loading">
            <p>Loading storybooks...</p>
          </div>
          
          <div *ngIf="!isLoading && (books$ | async)?.length === 0" class="empty-state">
            <p>No storybooks available.</p>
          </div>
          
          <div *ngIf="!isLoading" class="books-table">
            <table>
              <thead>
                <tr>
                  <th>Title</th>
                  <th>Author</th>
                  <th>Age Range</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let book of (books$ | async)">
                  <td>{{ book.title }}</td>
                  <td>{{ book.author }}</td>
                  <td>{{ book.ageRange }}</td>
                  <td>{{ book.createdAt | date:'mediumDate' }}</td>
                  <td class="actions">
                    <button class="icon-button edit" (click)="editBook(book.id)">
                      Edit
                    </button>
                    <button class="icon-button delete" (click)="confirmDelete(book)">
                      Delete
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    
    <div *ngIf="showDeleteModal" class="modal-backdrop">
      <div class="modal">
        <div class="modal-header">
          <h2>Confirm Deletion</h2>
        </div>
        <div class="modal-body">
          <p>
            Are you sure you want to delete "{{ bookToDelete?.title }}"? 
            This action cannot be undone.
          </p>
        </div>
        <div class="modal-footer">
          <button class="secondary" (click)="cancelDelete()">Cancel</button>
          <button class="danger" (click)="deleteBook()">Delete</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .admin-container {
      padding: var(--spacing-lg) 0 var(--spacing-xxl);
    }
    
    .admin-header {
      text-align: center;
      margin-bottom: var(--spacing-xl);
    }
    
    .admin-title {
      color: var(--primary-dark);
      margin-bottom: var(--spacing-sm);
    }
    
    .admin-subtitle {
      color: var(--text-secondary);
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .admin-actions {
      text-align: right;
      margin-bottom: var(--spacing-lg);
    }
    
    .admin-actions button {
      background-color: var(--accent);
      color: white;
      border: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .admin-actions button:hover {
      background-color: var(--accent-dark);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .admin-section {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    
    .section-header {
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .section-header h2 {
      color: var(--primary-dark);
      margin-bottom: var(--spacing-xs);
    }
    
    .section-header p {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    .loading, .empty-state {
      text-align: center;
      padding: var(--spacing-xl) 0;
      color: var(--text-secondary);
    }
    
    .books-table {
      overflow-x: auto;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
    }
    
    thead {
      background-color: rgba(106, 90, 205, 0.1);
    }
    
    th {
      text-align: left;
      padding: var(--spacing-md);
      font-weight: 600;
      color: var(--primary-dark);
      border-bottom: 2px solid rgba(106, 90, 205, 0.2);
    }
    
    td {
      padding: var(--spacing-md);
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      color: var(--text-primary);
    }
    
    td.actions {
      display: flex;
      gap: var(--spacing-sm);
    }
    
    .icon-button {
      background: none;
      border: none;
      cursor: pointer;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-weight: 600;
      font-size: 0.9rem;
      transition: all 0.3s ease;
    }
    
    .icon-button.edit {
      color: var(--primary);
    }
    
    .icon-button.edit:hover {
      background-color: rgba(106, 90, 205, 0.1);
    }
    
    .icon-button.delete {
      color: var(--error);
    }
    
    .icon-button.delete:hover {
      background-color: rgba(244, 67, 54, 0.1);
    }
    
    /* Modal Styles */
    .modal-backdrop {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    
    .modal {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      width: 90%;
      max-width: 500px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      animation: slideUp 0.3s ease forwards;
    }
    
    .modal-header {
      padding: var(--spacing-md) var(--spacing-lg);
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .modal-header h2 {
      color: var(--primary-dark);
      font-size: 1.2rem;
      margin: 0;
    }
    
    .modal-body {
      padding: var(--spacing-lg);
    }
    
    .modal-footer {
      padding: var(--spacing-md) var(--spacing-lg);
      border-top: 1px solid rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-md);
    }
    
    .modal-footer button {
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .modal-footer button.secondary {
      background-color: rgba(0, 0, 0, 0.1);
      color: var(--text-primary);
    }
    
    .modal-footer button.secondary:hover {
      background-color: rgba(0, 0, 0, 0.2);
    }
    
    .modal-footer button.danger {
      background-color: var(--error);
      color: white;
    }
    
    .modal-footer button.danger:hover {
      background-color: #d32f2f;
    }
    
    @media (max-width: 768px) {
      table {
        min-width: 600px;
      }
    }
  `]
})
export class AdminDashboardComponent implements OnInit {
  books$!: Observable<Book[]>;
  isLoading = true;
  showDeleteModal = false;
  bookToDelete: Book | null = null;
  
  constructor(
    private bookService: BookService,
    private router: Router
  ) { }
  
  ngOnInit(): void {
    this.loadBooks();
  }
  
  loadBooks(): void {
    this.isLoading = true;
    this.books$ = this.bookService.getBooks();
    
    // Set loading to false after a short delay
    setTimeout(() => {
      this.isLoading = false;
    }, 500);
  }
  
  editBook(bookId: string): void {
    this.router.navigate(['/admin/upload'], { queryParams: { id: bookId } });
  }
  
  confirmDelete(book: Book): void {
    this.bookToDelete = book;
    this.showDeleteModal = true;
  }
  
  cancelDelete(): void {
    this.bookToDelete = null;
    this.showDeleteModal = false;
  }
  
  deleteBook(): void {
    if (this.bookToDelete) {
      this.bookService.deleteBook(this.bookToDelete.id).subscribe({
        next: () => {
          this.showDeleteModal = false;
          this.bookToDelete = null;
          this.loadBooks();
        },
        error: (error) => {
          console.error('Error deleting book:', error);
          this.showDeleteModal = false;
          this.bookToDelete = null;
        }
      });
    }
  }
}