import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-login',
  imports: [CommonModule, ReactiveFormsModule, RouterLink],
  template: `
    <div class="auth-container">
      <div class="container">
        <div class="auth-card">
          <h1 class="auth-title">Welcome Back!</h1>
          <p class="auth-subtitle">Log in to access your favorite storybooks.</p>

          <div *ngIf="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>

          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
            <div class="form-group">
              <label for="email">Email</label>
              <input
                type="email"
                id="email"
                formControlName="email"
                placeholder="Your email address"
                [class.error]="isFieldInvalid('email')"
              >
              <div *ngIf="isFieldInvalid('email')" class="error-text">
                Please enter a valid email address.
              </div>
            </div>

            <div class="form-group">
              <label for="password">Password</label>
              <input
                type="password"
                id="password"
                formControlName="password"
                placeholder="Your password"
                [class.error]="isFieldInvalid('password')"
              >
              <div *ngIf="isFieldInvalid('password')" class="error-text">
                Password is required.
              </div>
            </div>

            <button
              type="submit"
              class="auth-button"
              [disabled]="loginForm.invalid || isLoading"
            >
              <span *ngIf="!isLoading">Log In</span>
              <span *ngIf="isLoading">Logging in...</span>
            </button>
          </form>

          <div class="demo-accounts">
            <p>Demo Accounts:</p>
            <div class="demo-buttons">
              <button class="demo-button" (click)="fillDemoAccount('user')">User Account</button>
              <button class="demo-button admin" (click)="fillDemoAccount('admin')">Admin Account</button>
            </div>
          </div>

          <div class="auth-footer">
            <p>Don't have an account? <a routerLink="/register">Sign Up</a></p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .auth-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: calc(100vh - 70px - 200px);
      padding: var(--spacing-xl) 0;
    }

    .auth-card {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      padding: var(--spacing-xl);
      width: 100%;
      max-width: 450px;
      margin: 0 auto;
      animation: slideUp 0.5s ease forwards;
    }

    .auth-title {
      text-align: center;
      color: var(--primary);
      margin-bottom: var(--spacing-sm);
    }

    .auth-subtitle {
      text-align: center;
      color: var(--text-secondary);
      margin-bottom: var(--spacing-lg);
    }

    .auth-form {
      margin-bottom: var(--spacing-lg);
    }

    .form-group {
      margin-bottom: var(--spacing-md);
    }

    label {
      display: block;
      margin-bottom: var(--spacing-xs);
      font-weight: 600;
      color: var(--text-primary);
    }

    input {
      width: 100%;
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      border: 1px solid rgba(0, 0, 0, 0.1);
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    input:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(106, 90, 205, 0.2);
    }

    input.error {
      border-color: var(--error);
    }

    .error-text {
      color: var(--error);
      font-size: 0.8rem;
      margin-top: var(--spacing-xs);
    }

    .error-message {
      background-color: rgba(244, 67, 54, 0.1);
      color: var(--error);
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-md);
      text-align: center;
    }

    .auth-button {
      width: 100%;
      padding: var(--spacing-md);
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.3s ease;
    }

    .auth-button:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }

    .auth-button:disabled {
      background-color: rgba(106, 90, 205, 0.5);
      cursor: not-allowed;
    }

    .auth-footer {
      text-align: center;
      margin-top: var(--spacing-lg);
      color: var(--text-secondary);
    }

    .auth-footer a {
      color: var(--primary);
      font-weight: 600;
      transition: color 0.3s ease;
    }

    .auth-footer a:hover {
      color: var(--primary-dark);
    }

    .demo-accounts {
      margin-top: var(--spacing-lg);
      padding-top: var(--spacing-md);
      border-top: 1px solid rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .demo-accounts p {
      margin-bottom: var(--spacing-sm);
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    .demo-buttons {
      display: flex;
      gap: var(--spacing-md);
    }

    .demo-button {
      flex: 1;
      padding: var(--spacing-sm);
      background-color: rgba(106, 90, 205, 0.1);
      color: var(--primary);
      border: none;
      border-radius: var(--radius-md);
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .demo-button:hover {
      background-color: rgba(106, 90, 205, 0.2);
    }

    .demo-button.admin {
      background-color: rgba(255, 150, 66, 0.1);
      color: var(--accent-dark);
    }

    .demo-button.admin:hover {
      background-color: rgba(255, 150, 66, 0.2);
    }
  `]
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  isLoading = false;
  errorMessage = '';
  returnUrl = '';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required]
    });

    // Get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';

    // Redirect if already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/']);
    }
  }

  onSubmit(): void {
    if (this.loginForm.invalid || this.isLoading) {
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.authService.login(this.loginForm.value).subscribe({
      next: () => {
        this.router.navigateByUrl(this.returnUrl);
      },
      error: (error) => {
        this.errorMessage = error.message;
        this.isLoading = false;
      }
    });
  }

  isFieldInvalid(field: string): boolean {
    const formControl = this.loginForm.get(field);
    return !!formControl && formControl.invalid && (formControl.dirty || formControl.touched);
  }

  fillDemoAccount(accountType: 'user' | 'admin'): void {
    if (accountType === 'user') {
      this.loginForm.patchValue({
        email: '<EMAIL>',
        password: 'user123'
      });
    } else {
      this.loginForm.patchValue({
        email: '<EMAIL>',
        password: 'admin123'
      });
    }
  }
}