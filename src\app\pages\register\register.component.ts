import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-register',
  template: `
    <div class="auth-container">
      <div class="container">
        <div class="auth-card">
          <h1 class="auth-title">Join <PERSON></h1>
          <p class="auth-subtitle">Create an account to access all our storybooks!</p>
          
          <div *ngIf="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          
          <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="auth-form">
            <div class="form-group">
              <label for="username">Username</label>
              <input 
                type="text" 
                id="username" 
                formControlName="username" 
                placeholder="Choose a username"
                [class.error]="isFieldInvalid('username')"
              >
              <div *ngIf="isFieldInvalid('username')" class="error-text">
                Username is required (at least 3 characters).
              </div>
            </div>
            
            <div class="form-group">
              <label for="email">Email</label>
              <input 
                type="email" 
                id="email" 
                formControlName="email" 
                placeholder="Your email address"
                [class.error]="isFieldInvalid('email')"
              >
              <div *ngIf="isFieldInvalid('email')" class="error-text">
                Please enter a valid email address.
              </div>
            </div>
            
            <div class="form-group">
              <label for="password">Password</label>
              <input 
                type="password" 
                id="password" 
                formControlName="password" 
                placeholder="Create a password"
                [class.error]="isFieldInvalid('password')"
              >
              <div *ngIf="isFieldInvalid('password')" class="error-text">
                Password must be at least 6 characters.
              </div>
            </div>
            
            <div class="form-group">
              <label for="confirmPassword">Confirm Password</label>
              <input 
                type="password" 
                id="confirmPassword" 
                formControlName="confirmPassword" 
                placeholder="Confirm your password"
                [class.error]="isFieldInvalid('confirmPassword')"
              >
              <div *ngIf="isFieldInvalid('confirmPassword')" class="error-text">
                Passwords do not match.
              </div>
            </div>
            
            <button 
              type="submit" 
              class="auth-button" 
              [disabled]="registerForm.invalid || isLoading"
            >
              <span *ngIf="!isLoading">Create Account</span>
              <span *ngIf="isLoading">Creating Account...</span>
            </button>
          </form>
          
          <div class="auth-footer">
            <p>Already have an account? <a routerLink="/login">Log In</a></p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .auth-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: calc(100vh - 70px - 200px);
      padding: var(--spacing-xl) 0;
    }
    
    .auth-card {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      padding: var(--spacing-xl);
      width: 100%;
      max-width: 450px;
      margin: 0 auto;
      animation: slideUp 0.5s ease forwards;
    }
    
    .auth-title {
      text-align: center;
      color: var(--primary);
      margin-bottom: var(--spacing-sm);
    }
    
    .auth-subtitle {
      text-align: center;
      color: var(--text-secondary);
      margin-bottom: var(--spacing-lg);
    }
    
    .auth-form {
      margin-bottom: var(--spacing-lg);
    }
    
    .form-group {
      margin-bottom: var(--spacing-md);
    }
    
    label {
      display: block;
      margin-bottom: var(--spacing-xs);
      font-weight: 600;
      color: var(--text-primary);
    }
    
    input {
      width: 100%;
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      border: 1px solid rgba(0, 0, 0, 0.1);
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }
    
    input:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(106, 90, 205, 0.2);
    }
    
    input.error {
      border-color: var(--error);
    }
    
    .error-text {
      color: var(--error);
      font-size: 0.8rem;
      margin-top: var(--spacing-xs);
    }
    
    .error-message {
      background-color: rgba(244, 67, 54, 0.1);
      color: var(--error);
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-md);
      text-align: center;
    }
    
    .auth-button {
      width: 100%;
      padding: var(--spacing-md);
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.3s ease;
    }
    
    .auth-button:hover:not(:disabled) {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }
    
    .auth-button:disabled {
      background-color: rgba(106, 90, 205, 0.5);
      cursor: not-allowed;
    }
    
    .auth-footer {
      text-align: center;
      margin-top: var(--spacing-lg);
      color: var(--text-secondary);
    }
    
    .auth-footer a {
      color: var(--primary);
      font-weight: 600;
      transition: color 0.3s ease;
    }
    
    .auth-footer a:hover {
      color: var(--primary-dark);
    }
  `]
})
export class RegisterComponent implements OnInit {
  registerForm!: FormGroup;
  isLoading = false;
  errorMessage = '';
  
  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) { }
  
  ngOnInit(): void {
    this.registerForm = this.formBuilder.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required]
    }, {
      validators: this.passwordMatchValidator
    });
    
    // Redirect if already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/']);
    }
  }
  
  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;
    
    if (password !== confirmPassword) {
      form.get('confirmPassword')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else {
      return null;
    }
  }
  
  onSubmit(): void {
    if (this.registerForm.invalid || this.isLoading) {
      return;
    }
    
    this.isLoading = true;
    this.errorMessage = '';
    
    const { username, email, password } = this.registerForm.value;
    
    this.authService.register({ username, email, password }).subscribe({
      next: () => {
        this.router.navigate(['/']);
      },
      error: (error) => {
        this.errorMessage = error.message;
        this.isLoading = false;
      }
    });
  }
  
  isFieldInvalid(field: string): boolean {
    const formControl = this.registerForm.get(field);
    return !!formControl && formControl.invalid && (formControl.dirty || formControl.touched);
  }
}