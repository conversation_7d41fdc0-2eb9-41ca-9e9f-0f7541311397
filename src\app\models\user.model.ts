export interface User {
  id: string;
  username: string;
  email: string;
  isAdmin: boolean;
  favorites?: string[];
  progress?: { [key: string]: number };
  createdAt: Date;
}

export interface AuthResponse {
  user: User;
  token: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  username: string;
  email: string;
  password: string;
}