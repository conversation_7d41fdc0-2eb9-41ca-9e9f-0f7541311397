import { Routes } from '@angular/router';
import { HomeComponent } from './pages/home/<USER>';
import { LoginComponent } from './pages/login/login.component';
import { RegisterComponent } from './pages/register/register.component';
import { BookCatalogComponent } from './pages/book-catalog/book-catalog.component';
import { BookReaderComponent } from './pages/book-reader/book-reader.component';
import { AdminDashboardComponent } from './pages/admin/admin-dashboard/admin-dashboard.component';
import { BookUploadComponent } from './pages/admin/book-upload/book-upload.component';
import { UserProfileComponent } from './pages/user-profile/user-profile.component';
import { AuthGuard } from './guards/auth.guard';
import { AdminGuard } from './guards/admin.guard';

export const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },
  { path: 'books', component: BookCatalogComponent },
  { path: 'books/:id', component: BookReaderComponent, canActivate: [AuthGuard] },
  { path: 'profile', component: UserProfileComponent, canActivate: [AuthGuard] },
  { 
    path: 'admin', 
    canActivate: [AdminGuard],
    children: [
      { path: '', component: AdminDashboardComponent },
      { path: 'upload', component: BookUploadComponent }
    ]
  },
  { path: '**', redirectTo: '' }
];