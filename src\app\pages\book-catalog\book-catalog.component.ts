import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, combineLatest, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { Book, BookFilter } from '../../models/book.model';
import { BookService } from '../../services/book.service';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-book-catalog',
  template: `
    <div class="catalog-container">
      <div class="container">
        <div class="catalog-header">
          <h1 class="catalog-title">Storybook Catalog</h1>
          <p class="catalog-description">
            Discover magical stories for children of all ages.
          </p>
        </div>
        
        <div class="filter-bar">
          <div class="search-box">
            <input 
              type="text" 
              placeholder="Search for stories..." 
              [(ngModel)]="searchTerm"
              (keyup.enter)="applyFilters()"
            >
            <button class="search-button" (click)="applyFilters()">Search</button>
          </div>
          
          <div class="filter-options">
            <div class="filter-group">
              <label for="category-filter">Category:</label>
              <select id="category-filter" [(ngModel)]="selectedCategory" (change)="applyFilters()">
                <option value="">All Categories</option>
                <option value="Adventure">Adventure</option>
                <option value="Fantasy">Fantasy</option>
                <option value="Animals">Animals</option>
                <option value="Bedtime">Bedtime</option>
                <option value="Dinosaurs">Dinosaurs</option>
                <option value="Ocean">Ocean</option>
                <option value="Friendship">Friendship</option>
                <option value="Sharing">Sharing</option>
              </select>
            </div>
            
            <div class="filter-group">
              <label for="age-filter">Age Range:</label>
              <select id="age-filter" [(ngModel)]="selectedAgeRange" (change)="applyFilters()">
                <option value="">All Ages</option>
                <option value="3-5">Ages 3-5</option>
                <option value="4-6">Ages 4-6</option>
                <option value="5-8">Ages 5-8</option>
                <option value="8-12">Ages 8-12</option>
              </select>
            </div>
          </div>
        </div>
        
        <div *ngIf="isLoading" class="loading">
          <p>Loading storybooks...</p>
        </div>
        
        <div *ngIf="!isLoading && (books$ | async)?.length === 0" class="no-results">
          <p>No storybooks found matching your criteria.</p>
          <button (click)="clearFilters()">Clear Filters</button>
        </div>
        
        <div *ngIf="!isLoading" class="books-grid">
          <ng-container *ngFor="let book of (books$ | async)">
            <app-book-card
              [book]="book"
              [isFavorite]="isBookFavorited(book.id)"
              (readClicked)="onReadBook($event)"
              (favoriteToggled)="onToggleFavorite($event)"
            ></app-book-card>
          </ng-container>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .catalog-container {
      padding: var(--spacing-lg) 0 var(--spacing-xxl);
    }
    
    .catalog-header {
      text-align: center;
      margin-bottom: var(--spacing-xl);
    }
    
    .catalog-title {
      color: var(--primary-dark);
      margin-bottom: var(--spacing-sm);
    }
    
    .catalog-description {
      color: var(--text-secondary);
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .filter-bar {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-lg);
    }
    
    .search-box {
      flex: 1;
      min-width: 250px;
      display: flex;
    }
    
    .search-box input {
      flex: 1;
      padding: var(--spacing-md);
      border-radius: var(--radius-md) 0 0 var(--radius-md);
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-right: none;
    }
    
    .search-button {
      background-color: var(--primary);
      color: white;
      border: none;
      padding: 0 var(--spacing-lg);
      border-radius: 0 var(--radius-md) var(--radius-md) 0;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }
    
    .search-button:hover {
      background-color: var(--primary-dark);
    }
    
    .filter-options {
      display: flex;
      gap: var(--spacing-md);
      flex-wrap: wrap;
    }
    
    .filter-group {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }
    
    .filter-group label {
      font-weight: 600;
      color: var(--text-secondary);
    }
    
    .filter-group select {
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-md);
      border: 1px solid rgba(0, 0, 0, 0.1);
      background-color: white;
      min-width: 150px;
    }
    
    .books-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: var(--spacing-lg);
    }
    
    .loading, .no-results {
      text-align: center;
      padding: var(--spacing-xl) 0;
      color: var(--text-secondary);
    }
    
    .no-results button {
      margin-top: var(--spacing-md);
      background-color: var(--primary);
      color: white;
      border: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: background-color 0.3s ease;
    }
    
    .no-results button:hover {
      background-color: var(--primary-dark);
    }
    
    @media (max-width: 768px) {
      .filter-bar {
        flex-direction: column;
        gap: var(--spacing-md);
      }
      
      .filter-options {
        flex-direction: column;
        width: 100%;
      }
      
      .filter-group {
        width: 100%;
      }
      
      .filter-group select {
        flex: 1;
      }
    }
  `]
})
export class BookCatalogComponent implements OnInit {
  books$!: Observable<Book[]>;
  isLoading = true;
  searchTerm = '';
  selectedCategory = '';
  selectedAgeRange = '';
  
  constructor(
    private bookService: BookService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) { }
  
  ngOnInit(): void {
    // Subscribe to query params
    this.route.queryParams.subscribe(params => {
      if (params['category']) {
        this.selectedCategory = params['category'];
      }
      
      if (params['ageRange']) {
        this.selectedAgeRange = params['ageRange'];
      }
      
      if (params['searchTerm']) {
        this.searchTerm = params['searchTerm'];
      }
      
      this.loadBooks();
    });
  }
  
  loadBooks(): void {
    this.isLoading = true;
    
    const filter: BookFilter = {};
    
    if (this.selectedCategory) {
      filter.category = this.selectedCategory;
    }
    
    if (this.selectedAgeRange) {
      filter.ageRange = this.selectedAgeRange;
    }
    
    if (this.searchTerm) {
      filter.searchTerm = this.searchTerm;
    }
    
    this.books$ = this.bookService.getBooks(filter);
    
    // Set loading to false after a short delay to show the loading state
    setTimeout(() => {
      this.isLoading = false;
    }, 500);
  }
  
  applyFilters(): void {
    // Update URL with filters
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        category: this.selectedCategory || null,
        ageRange: this.selectedAgeRange || null,
        searchTerm: this.searchTerm || null
      },
      queryParamsHandling: 'merge'
    });
    
    this.loadBooks();
  }
  
  clearFilters(): void {
    this.selectedCategory = '';
    this.selectedAgeRange = '';
    this.searchTerm = '';
    
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {}
    });
    
    this.loadBooks();
  }
  
  onReadBook(bookId: string): void {
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/books', bookId]);
    } else {
      this.router.navigate(['/login'], {
        queryParams: { returnUrl: `/books/${bookId}` }
      });
    }
  }
  
  onToggleFavorite(bookId: string): void {
    if (this.authService.isAuthenticated()) {
      this.bookService.toggleFavorite(bookId).subscribe();
    } else {
      this.router.navigate(['/login']);
    }
  }
  
  isBookFavorited(bookId: string): boolean {
    const currentUser = this.authService.getCurrentUser();
    return currentUser?.favorites?.includes(bookId) || false;
  }
}