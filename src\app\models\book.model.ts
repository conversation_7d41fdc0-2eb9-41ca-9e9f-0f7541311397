export interface Book {
  id: string;
  title: string;
  author: string;
  coverImage: string;
  description: string;
  ageRange: string;
  categories: string[];
  pages: BookPage[];
  createdAt: Date;
  createdBy: string;
}

export interface BookPage {
  pageNumber: number;
  content: string;
  image?: string;
}

export interface BookFilter {
  category?: string;
  ageRange?: string;
  searchTerm?: string;
}