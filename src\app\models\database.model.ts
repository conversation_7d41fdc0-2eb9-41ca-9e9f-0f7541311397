// Database models for Supabase tables

export interface DatabaseUser {
  id: string;
  username: string;
  email: string;
  password_hash: string;
  is_admin: boolean;
  created_at: string;
  updated_at: string;
}

export interface DatabaseBook {
  id: string;
  title: string;
  author: string;
  cover_image: string;
  description: string;
  age_range: string;
  categories: string[];
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseBookPage {
  id: string;
  book_id: string;
  page_number: number;
  content: string;
  image?: string;
  created_at: string;
}

export interface DatabaseUserFavorite {
  id: string;
  user_id: string;
  book_id: string;
  created_at: string;
}

export interface DatabaseUserProgress {
  id: string;
  user_id: string;
  book_id: string;
  current_page: number;
  total_pages: number;
  updated_at: string;
}

// Response types
export interface SupabaseResponse<T> {
  data: T | null;
  error: any;
}

export interface SupabaseListResponse<T> {
  data: T[] | null;
  error: any;
  count?: number;
}
