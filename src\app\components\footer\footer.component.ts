import { Component } from '@angular/core';

@Component({
  selector: 'app-footer',
  template: `
    <footer class="footer">
      <div class="container footer-container">
        <div class="footer-section">
          <h3 class="footer-title">StoryWonder</h3>
          <p class="footer-description">
            A magical collection of storybooks for children of all ages.
            Discover new adventures, learn valuable lessons, and enjoy
            wonderful stories together.
          </p>
        </div>
        
        <div class="footer-section">
          <h3 class="footer-title">Navigation</h3>
          <ul class="footer-links">
            <li><a routerLink="/">Home</a></li>
            <li><a routerLink="/books">Storybooks</a></li>
            <li><a routerLink="/login">Login</a></li>
            <li><a routerLink="/register">Sign Up</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3 class="footer-title">Categories</h3>
          <ul class="footer-links">
            <li><a routerLink="/books" [queryParams]="{category: 'Adventure'}">Adventure</a></li>
            <li><a routerLink="/books" [queryParams]="{category: 'Fantasy'}">Fantasy</a></li>
            <li><a routerLink="/books" [queryParams]="{category: 'Animals'}">Animals</a></li>
            <li><a routerLink="/books" [queryParams]="{category: 'Bedtime'}">Bedtime</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3 class="footer-title">Age Groups</h3>
          <ul class="footer-links">
            <li><a routerLink="/books" [queryParams]="{ageRange: '3-5'}">Ages 3-5</a></li>
            <li><a routerLink="/books" [queryParams]="{ageRange: '4-6'}">Ages 4-6</a></li>
            <li><a routerLink="/books" [queryParams]="{ageRange: '5-8'}">Ages 5-8</a></li>
            <li><a routerLink="/books" [queryParams]="{ageRange: '8-12'}">Ages 8-12</a></li>
          </ul>
        </div>
      </div>
      
      <div class="footer-bottom">
        <div class="container">
          <p>&copy; 2025 StoryWonder. All rights reserved.</p>
        </div>
      </div>
    </footer>
  `,
  styles: [`
    .footer {
      background-color: var(--primary-dark);
      color: white;
      padding-top: var(--spacing-xl);
      margin-top: auto;
    }
    
    .footer-container {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--spacing-xl);
    }
    
    .footer-title {
      color: white;
      margin-bottom: var(--spacing-md);
      font-size: 1.2rem;
    }
    
    .footer-description {
      line-height: 1.6;
      margin-bottom: var(--spacing-md);
      color: rgba(255, 255, 255, 0.8);
    }
    
    .footer-links {
      list-style: none;
      padding: 0;
    }
    
    .footer-links li {
      margin-bottom: var(--spacing-sm);
    }
    
    .footer-links a {
      color: rgba(255, 255, 255, 0.8);
      transition: color 0.3s ease;
      text-decoration: none;
    }
    
    .footer-links a:hover {
      color: white;
      text-decoration: none;
    }
    
    .footer-bottom {
      margin-top: var(--spacing-xl);
      padding: var(--spacing-md) 0;
      text-align: center;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      font-size: 0.9rem;
      color: rgba(255, 255, 255, 0.7);
    }
    
    @media (max-width: 992px) {
      .footer-container {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    @media (max-width: 576px) {
      .footer-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }
    }
  `]
})
export class FooterComponent {
}