import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://tiwvctldwswlirhzsikl.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRpd3ZjdGxkd3N3bGlyaHpzaWtsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQwNjgyMSwiZXhwIjoyMDYzOTgyODIxfQ.amZUH-I3PpJlGXquZ_9uSXLPVqxNoXVx0aexgnbpxxk';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseKey);

// Database table names
export const TABLES = {
  USERS: 'users',
  BOOKS: 'books',
  BOOK_PAGES: 'book_pages',
  USER_FAVORITES: 'user_favorites',
  USER_PROGRESS: 'user_progress'
} as const;
