import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Book } from '../../../models/book.model';
import { BookService } from '../../../services/book.service';

@Component({
  selector: 'app-book-upload',
  template: `
    <div class="upload-container">
      <div class="container">
        <div class="upload-header">
          <h1 class="upload-title">{{ isEditMode ? 'Edit Storybook' : 'Upload New Storybook' }}</h1>
          <p class="upload-subtitle">{{ isEditMode ? 'Update the details of this storybook' : 'Add a new storybook to the catalog' }}</p>
        </div>
        
        <div class="upload-form-container">
          <div *ngIf="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          
          <div *ngIf="isLoading" class="loading">
            <p>{{ isEditMode ? 'Loading storybook data...' : 'Preparing form...' }}</p>
          </div>
          
          <form *ngIf="!isLoading" [formGroup]="bookForm" (ngSubmit)="onSubmit()" class="upload-form">
            <div class="form-section">
              <h2>Basic Information</h2>
              
              <div class="form-group">
                <label for="title">Title</label>
                <input 
                  type="text" 
                  id="title" 
                  formControlName="title" 
                  placeholder="Enter the storybook title"
                  [class.error]="isFieldInvalid('title')"
                >
                <div *ngIf="isFieldInvalid('title')" class="error-text">
                  Title is required.
                </div>
              </div>
              
              <div class="form-group">
                <label for="author">Author</label>
                <input 
                  type="text" 
                  id="author" 
                  formControlName="author" 
                  placeholder="Enter the author's name"
                  [class.error]="isFieldInvalid('author')"
                >
                <div *ngIf="isFieldInvalid('author')" class="error-text">
                  Author is required.
                </div>
              </div>
              
              <div class="form-group">
                <label for="coverImage">Cover Image URL</label>
                <input 
                  type="text" 
                  id="coverImage" 
                  formControlName="coverImage" 
                  placeholder="Enter the URL for the cover image"
                  [class.error]="isFieldInvalid('coverImage')"
                >
                <div *ngIf="isFieldInvalid('coverImage')" class="error-text">
                  Cover image URL is required.
                </div>
                <div *ngIf="bookForm.get('coverImage')?.value" class="image-preview">
                  <img [src]="bookForm.get('coverImage')?.value" alt="Cover preview">
                </div>
              </div>
              
              <div class="form-group">
                <label for="description">Description</label>
                <textarea 
                  id="description" 
                  formControlName="description" 
                  placeholder="Enter a description of the storybook"
                  rows="4"
                  [class.error]="isFieldInvalid('description')"
                ></textarea>
                <div *ngIf="isFieldInvalid('description')" class="error-text">
                  Description is required.
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="ageRange">Age Range</label>
                  <select 
                    id="ageRange" 
                    formControlName="ageRange"
                    [class.error]="isFieldInvalid('ageRange')"
                  >
                    <option value="">Select an age range</option>
                    <option value="3-5">Ages 3-5</option>
                    <option value="4-6">Ages 4-6</option>
                    <option value="5-8">Ages 5-8</option>
                    <option value="8-12">Ages 8-12</option>
                  </select>
                  <div *ngIf="isFieldInvalid('ageRange')" class="error-text">
                    Age range is required.
                  </div>
                </div>
                
                <div class="form-group">
                  <label>Categories</label>
                  <div class="categories-container" [class.error]="isFieldInvalid('categories')">
                    <div class="category-options">
                      <label class="category-label" *ngFor="let category of availableCategories">
                        <input 
                          type="checkbox" 
                          [value]="category"
                          (change)="onCategoryChange($event)"
                          [checked]="isCategorySelected(category)"
                        >
                        {{ category }}
                      </label>
                    </div>
                  </div>
                  <div *ngIf="isFieldInvalid('categories')" class="error-text">
                    Select at least one category.
                  </div>
                </div>
              </div>
            </div>
            
            <div class="form-section">
              <h2>Storybook Pages</h2>
              <p class="section-info">Add the content and images for each page of the storybook.</p>
              
              <div formArrayName="pages">
                <div *ngFor="let page of pages.controls; let i = index" [formGroupName]="i" class="page-form">
                  <div class="page-header">
                    <h3>Page {{ i + 1 }}</h3>
                    <button type="button" class="delete-button" (click)="removePage(i)" *ngIf="pages.length > 1">
                      Remove Page
                    </button>
                  </div>
                  
                  <div class="form-group">
                    <label [for]="'content-' + i">Content</label>
                    <textarea 
                      [id]="'content-' + i" 
                      formControlName="content" 
                      placeholder="Enter the text content for this page"
                      rows="3"
                      [class.error]="isPageFieldInvalid(i, 'content')"
                    ></textarea>
                    <div *ngIf="isPageFieldInvalid(i, 'content')" class="error-text">
                      Page content is required.
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label [for]="'image-' + i">Image URL (optional)</label>
                    <input 
                      type="text" 
                      [id]="'image-' + i" 
                      formControlName="image" 
                      placeholder="Enter the URL for an image on this page"
                    >
                    <div *ngIf="page.get('image')?.value" class="image-preview">
                      <img [src]="page.get('image')?.value" alt="Page image preview">
                    </div>
                  </div>
                </div>
              </div>
              
              <button type="button" class="add-page-button" (click)="addPage()">
                Add Another Page
              </button>
            </div>
            
            <div class="form-actions">
              <button type="button" class="cancel-button" (click)="cancel()">Cancel</button>
              <button 
                type="submit" 
                class="submit-button" 
                [disabled]="bookForm.invalid || isSubmitting"
              >
                <span *ngIf="!isSubmitting">{{ isEditMode ? 'Update Storybook' : 'Upload Storybook' }}</span>
                <span *ngIf="isSubmitting">{{ isEditMode ? 'Updating...' : 'Uploading...' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .upload-container {
      padding: var(--spacing-lg) 0 var(--spacing-xxl);
    }
    
    .upload-header {
      text-align: center;
      margin-bottom: var(--spacing-xl);
    }
    
    .upload-title {
      color: var(--primary-dark);
      margin-bottom: var(--spacing-sm);
    }
    
    .upload-subtitle {
      color: var(--text-secondary);
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .upload-form-container {
      background-color: var(--surface);
      border-radius: var(--radius-lg);
      padding: var(--spacing-xl);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    
    .error-message {
      background-color: rgba(244, 67, 54, 0.1);
      color: var(--error);
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-md);
    }
    
    .loading {
      text-align: center;
      padding: var(--spacing-xl) 0;
      color: var(--text-secondary);
    }
    
    .form-section {
      margin-bottom: var(--spacing-xl);
      padding-bottom: var(--spacing-lg);
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .form-section h2 {
      color: var(--primary-dark);
      margin-bottom: var(--spacing-sm);
      font-size: 1.3rem;
    }
    
    .section-info {
      color: var(--text-secondary);
      margin-bottom: var(--spacing-md);
      font-size: 0.9rem;
    }
    
    .form-group {
      margin-bottom: var(--spacing-md);
    }
    
    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }
    
    label {
      display: block;
      margin-bottom: var(--spacing-xs);
      font-weight: 600;
      color: var(--text-primary);
    }
    
    input, textarea, select {
      width: 100%;
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      border: 1px solid rgba(0, 0, 0, 0.1);
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }
    
    input:focus, textarea:focus, select:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(106, 90, 205, 0.2);
    }
    
    input.error, textarea.error, select.error, .categories-container.error {
      border-color: var(--error);
    }
    
    .error-text {
      color: var(--error);
      font-size: 0.8rem;
      margin-top: var(--spacing-xs);
    }
    
    .image-preview {
      margin-top: var(--spacing-sm);
      border-radius: var(--radius-md);
      overflow: hidden;
      max-width: 200px;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .image-preview img {
      width: 100%;
      height: auto;
      display: block;
    }
    
    .categories-container {
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: var(--radius-md);
      padding: var(--spacing-sm);
    }
    
    .category-options {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-sm);
    }
    
    .category-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      background-color: rgba(106, 90, 205, 0.1);
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-full);
      font-size: 0.9rem;
      font-weight: normal;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }
    
    .category-label:hover {
      background-color: rgba(106, 90, 205, 0.2);
    }
    
    .category-label input {
      width: auto;
      margin: 0;
    }
    
    .page-form {
      background-color: rgba(106, 90, 205, 0.05);
      border-radius: var(--radius-md);
      padding: var(--spacing-md);
      margin-bottom: var(--spacing-md);
    }
    
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-md);
    }
    
    .page-header h3 {
      color: var(--primary-dark);
      margin: 0;
      font-size: 1.1rem;
    }
    
    .delete-button {
      background-color: rgba(244, 67, 54, 0.1);
      color: var(--error);
      border: none;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-md);
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .delete-button:hover {
      background-color: rgba(244, 67, 54, 0.2);
    }
    
    .add-page-button {
      background-color: rgba(106, 90, 205, 0.1);
      color: var(--primary);
      border: none;
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-md);
      width: 100%;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .add-page-button:hover {
      background-color: rgba(106, 90, 205, 0.2);
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-md);
    }
    
    .cancel-button {
      background-color: rgba(0, 0, 0, 0.1);
      color: var(--text-primary);
      border: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .cancel-button:hover {
      background-color: rgba(0, 0, 0, 0.2);
    }
    
    .submit-button {
      background-color: var(--accent);
      color: white;
      border: none;
      padding: var(--spacing-sm) var(--spacing-lg);
      border-radius: var(--radius-md);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .submit-button:hover:not(:disabled) {
      background-color: var(--accent-dark);
      transform: translateY(-2px);
    }
    
    .submit-button:disabled {
      background-color: rgba(255, 150, 66, 0.5);
      cursor: not-allowed;
    }
    
    @media (max-width: 768px) {
      .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }
  `]
})
export class BookUploadComponent implements OnInit {
  bookForm!: FormGroup;
  isLoading = true;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;
  bookId = '';
  
  availableCategories = [
    'Adventure',
    'Fantasy',
    'Animals',
    'Bedtime',
    'Dinosaurs',
    'Ocean',
    'Friendship',
    'Sharing',
    'Educational',
    'Fairy Tale'
  ];
  
  constructor(
    private formBuilder: FormBuilder,
    private bookService: BookService,
    private router: Router,
    private route: ActivatedRoute
  ) { }
  
  ngOnInit(): void {
    this.initForm();
    
    // Check if we're in edit mode
    this.route.queryParams.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.bookId = params['id'];
        this.loadBook(this.bookId);
      } else {
        this.isLoading = false;
      }
    });
  }
  
  initForm(): void {
    this.bookForm = this.formBuilder.group({
      title: ['', Validators.required],
      author: ['', Validators.required],
      coverImage: ['', Validators.required],
      description: ['', Validators.required],
      ageRange: ['', Validators.required],
      categories: [[], [Validators.required, Validators.minLength(1)]],
      pages: this.formBuilder.array([])
    });
    
    // Add initial page
    this.addPage();
  }
  
  loadBook(id: string): void {
    this.bookService.getBookById(id).subscribe({
      next: (book) => {
        // Clear existing pages
        while (this.pages.length > 0) {
          this.pages.removeAt(0);
        }
        
        // Populate form with book data
        this.bookForm.patchValue({
          title: book.title,
          author: book.author,
          coverImage: book.coverImage,
          description: book.description,
          ageRange: book.ageRange,
          categories: book.categories
        });
        
        // Add pages
        book.pages.forEach(page => {
          this.pages.push(this.createPage(page.content, page.image));
        });
        
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading book: ' + error.message;
        this.isLoading = false;
      }
    });
  }
  
  get pages(): FormArray {
    return this.bookForm.get('pages') as FormArray;
  }
  
  createPage(content: string = '', image: string = ''): FormGroup {
    return this.formBuilder.group({
      content: [content, Validators.required],
      image: [image]
    });
  }
  
  addPage(): void {
    this.pages.push(this.createPage());
  }
  
  removePage(index: number): void {
    this.pages.removeAt(index);
    
    // Update page numbers
    for (let i = 0; i < this.pages.length; i++) {
      const page = this.pages.at(i);
      page.get('pageNumber')?.setValue(i + 1);
    }
  }
  
  onCategoryChange(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    const category = checkbox.value;
    const categories = this.bookForm.get('categories')?.value as string[];
    
    if (checkbox.checked) {
      if (!categories.includes(category)) {
        this.bookForm.get('categories')?.setValue([...categories, category]);
      }
    } else {
      this.bookForm.get('categories')?.setValue(categories.filter(c => c !== category));
    }
  }
  
  isCategorySelected(category: string): boolean {
    const categories = this.bookForm.get('categories')?.value as string[];
    return categories.includes(category);
  }
  
  isFieldInvalid(field: string): boolean {
    const control = this.bookForm.get(field);
    return !!control && control.invalid && (control.dirty || control.touched);
  }
  
  isPageFieldInvalid(pageIndex: number, field: string): boolean {
    const control = this.pages.at(pageIndex).get(field);
    return !!control && control.invalid && (control.dirty || control.touched);
  }
  
  onSubmit(): void {
    if (this.bookForm.invalid || this.isSubmitting) {
      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.bookForm);
      return;
    }
    
    this.isSubmitting = true;
    this.errorMessage = '';
    
    const formValue = this.bookForm.value;
    
    // Format pages with pageNumber
    const pages = formValue.pages.map((page: any, index: number) => ({
      ...page,
      pageNumber: index + 1
    }));
    
    const bookData = {
      ...formValue,
      pages
    };
    
    if (this.isEditMode) {
      this.bookService.updateBook(this.bookId, bookData).subscribe({
        next: () => {
          this.router.navigate(['/admin']);
        },
        error: (error) => {
          this.errorMessage = 'Error updating book: ' + error.message;
          this.isSubmitting = false;
        }
      });
    } else {
      this.bookService.addBook(bookData).subscribe({
        next: () => {
          this.router.navigate(['/admin']);
        },
        error: (error) => {
          this.errorMessage = 'Error adding book: ' + error.message;
          this.isSubmitting = false;
        }
      });
    }
  }
  
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(c => {
          if (c instanceof FormGroup) {
            this.markFormGroupTouched(c);
          } else {
            c.markAsTouched();
          }
        });
      }
    });
  }
  
  cancel(): void {
    this.router.navigate(['/admin']);
  }
}