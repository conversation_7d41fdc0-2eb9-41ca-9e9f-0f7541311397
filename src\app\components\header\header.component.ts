import { Component, OnInit } from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Observable } from 'rxjs';
import { User } from '../../models/user.model';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-header',
  imports: [CommonModule, RouterLink, RouterLinkActive],
  template: `
    <header class="header">
      <div class="container header-container">
        <div class="logo" routerLink="/">
          <span class="logo-text">StoryWonder</span>
          <div class="logo-icon">📚</div>
        </div>

        <nav class="nav-menu">
          <a routerLink="/" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">Home</a>
          <a routerLink="/books" routerLinkActive="active">Storybooks</a>
          <ng-container *ngIf="(currentUser$ | async) as user">
            <a routerLink="/profile" routerLinkActive="active">My Library</a>
            <a *ngIf="user.isAdmin" routerLink="/admin" routerLinkActive="active">Admin</a>
          </ng-container>
        </nav>

        <div class="auth-actions">
          <ng-container *ngIf="(currentUser$ | async) as user; else loginButtons">
            <div class="user-profile" [class.admin]="user.isAdmin">
              <span>{{ user.username }}</span>
              <button class="text" (click)="logout()">Logout</button>
            </div>
          </ng-container>

          <ng-template #loginButtons>
            <button class="text" routerLink="/login">Login</button>
            <button routerLink="/register">Sign Up</button>
          </ng-template>
        </div>

        <button class="mobile-menu-toggle" (click)="toggleMobileMenu()">
          <span class="menu-icon">☰</span>
        </button>
      </div>

      <div class="mobile-menu" [class.open]="isMobileMenuOpen">
        <a routerLink="/" (click)="closeMobileMenu()">Home</a>
        <a routerLink="/books" (click)="closeMobileMenu()">Storybooks</a>
        <ng-container *ngIf="(currentUser$ | async) as user">
          <a routerLink="/profile" (click)="closeMobileMenu()">My Library</a>
          <a *ngIf="user.isAdmin" routerLink="/admin" (click)="closeMobileMenu()">Admin</a>
          <a (click)="logout(); closeMobileMenu()">Logout</a>
        </ng-container>
        <ng-container *ngIf="!(currentUser$ | async)">
          <a routerLink="/login" (click)="closeMobileMenu()">Login</a>
          <a routerLink="/register" (click)="closeMobileMenu()">Sign Up</a>
        </ng-container>
      </div>
    </header>
  `,
  styles: [`
    .header {
      background-color: var(--surface);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .header-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-md) var(--spacing-lg);
      height: 70px;
    }

    .logo {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: transform 0.3s ease;
    }

    .logo:hover {
      transform: scale(1.05);
    }

    .logo-text {
      font-family: 'Quicksand', sans-serif;
      font-weight: 700;
      font-size: 1.5rem;
      color: var(--primary);
      margin-right: var(--spacing-sm);
    }

    .logo-icon {
      font-size: 1.5rem;
      animation: bounce 2s infinite;
    }

    .nav-menu {
      display: flex;
      gap: var(--spacing-lg);
    }

    .nav-menu a {
      font-weight: 600;
      color: var(--text-secondary);
      text-decoration: none;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      transition: all 0.3s ease;
    }

    .nav-menu a:hover {
      color: var(--primary);
      background-color: rgba(106, 90, 205, 0.1);
      text-decoration: none;
    }

    .nav-menu a.active {
      color: var(--primary);
      position: relative;
    }

    .nav-menu a.active::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 100%;
      height: 3px;
      background-color: var(--primary);
      border-radius: var(--radius-full);
    }

    .auth-actions {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
    }

    .user-profile {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      background-color: rgba(106, 90, 205, 0.1);
      padding: var(--spacing-xs) var(--spacing-md);
      border-radius: var(--radius-full);
    }

    .user-profile.admin {
      background-color: rgba(255, 150, 66, 0.2);
    }

    .user-profile span {
      font-weight: 600;
      color: var(--primary);
    }

    .user-profile.admin span {
      color: var(--accent-dark);
    }

    .mobile-menu-toggle {
      display: none;
      background: none;
      border: none;
      color: var(--primary);
      font-size: 1.5rem;
      cursor: pointer;
      padding: var(--spacing-xs);
    }

    .mobile-menu {
      display: none;
      flex-direction: column;
      position: fixed;
      top: 70px;
      left: 0;
      right: 0;
      background-color: var(--surface);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      transform: translateY(-100%);
      transition: transform 0.3s ease;
      z-index: 99;
    }

    .mobile-menu.open {
      transform: translateY(0);
    }

    .mobile-menu a {
      padding: var(--spacing-md);
      font-weight: 600;
      color: var(--text-secondary);
      text-decoration: none;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      transition: background-color 0.2s ease;
    }

    .mobile-menu a:hover {
      background-color: rgba(106, 90, 205, 0.1);
      color: var(--primary);
    }

    @media (max-width: 768px) {
      .nav-menu, .auth-actions {
        display: none;
      }

      .mobile-menu-toggle {
        display: block;
      }

      .mobile-menu {
        display: flex;
      }

      .header-container {
        padding: var(--spacing-md);
      }
    }
  `]
})
export class HeaderComponent implements OnInit {
  currentUser$: Observable<User | null>;
  isMobileMenuOpen = false;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {
    this.currentUser$ = this.authService.currentUser$;
  }

  ngOnInit(): void {
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/']);
  }

  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu(): void {
    this.isMobileMenuOpen = false;
  }
}